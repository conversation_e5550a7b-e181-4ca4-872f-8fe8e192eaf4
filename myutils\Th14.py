from influxdb_client import InfluxDBClient
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timezone
import numpy as np

# Connection Data
url = "http://*************:8086"
token = "4SPXQ9_jeeobEkc3hswm2Kdd93C9a3fmchDRIqePg8IzMNem7AQhoo7BCiPFht4cDszS5mPrcjMrxcL5YfPf3g=="
org = "IPQ"
bucket = "IPQ_SensorData"


def retrieve_sensor_data_for_measurement_type(sensor_id, measurement_type, start_datetime=None, stop_datetime=None):
    """
    Retrieve sensor data for a specific sensor and measurement type from InfluxDB.

    Parameters:
    -----------
    sensor_id : str
        The sensor device name (e.g., 'IPQ_TH14', 'IPQ_TH11')
    measurement_type : str
        The measurement type ('temperature' or 'humidity')
    start_datetime : str, optional
        Start time in ISO format (default: '2025-08-07T00:00:00Z')
    stop_datetime : str, optional
        Stop time in ISO format (default: current time)

    Returns:
    --------
    pandas.DataFrame
        DataFrame with time index and 'value' column containing the sensor data
    """
    if start_datetime is None:
        start_datetime = start_datetime
    if stop_datetime is None:
        stop_datetime = stop_datetime

    # Map measurement type to InfluxDB measurement name
    measurement_map = {
        'temperature': 'device_frmpayload_data_TempC_SHT',
        'humidity': 'device_frmpayload_data_Hum_SHT'
    }

    if measurement_type not in measurement_map:
        raise ValueError(f"Invalid measurement_type. Must be one of: {list(measurement_map.keys())}")

    measurement_name = measurement_map[measurement_type]

    # Flux Query
    query = f'''
    from(bucket: "{bucket}")
      |> range(start: {start_datetime}, stop: {stop_datetime})
      |> filter(fn: (r) => r["device_name"] == "{sensor_id}")
      |> filter(fn: (r) => r["_field"] == "value")
      |> filter(fn: (r) => r["SensorTyp"] == "Temperature Humidity Sensor")
      |> filter(fn: (r) => r["application_name"] == "Klimasensoren")
      |> filter(fn: (r) => r["_measurement"] == "{measurement_name}")
      |> aggregateWindow(every: 1m, fn: mean, createEmpty: false)
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> yield(name: "mean")
    '''

    # InfluxDB Client
    client = InfluxDBClient(url=url, token=token, org=org)
    query_api = client.query_api()

    try:
        # Query data
        df = query_api.query_data_frame(query=query, org=org)

        if df.empty:
            print(f"Warning: No data found for sensor {sensor_id} and measurement type {measurement_type}")
            return pd.DataFrame()

        # Convert _time to datetime and set as index
        df['_time'] = pd.to_datetime(df['_time'])
        df = df.set_index('_time')

        return df[['value']]  # Return only the value column

    except Exception as e:
        print(f"Error retrieving data for sensor {sensor_id}: {e}")
        return pd.DataFrame()
    finally:
        client.close()


def plot_multiple_sensors_temperature_and_humidity(sensor_ids, start_datetime=None, stop_datetime=None,
                                                   plot_temperature=True, plot_humidity=True,
                                                   figsize=(15, 10), save_plot=False, filename=None):
    """
    Plot temperature and humidity data from multiple sensors on the same graph.

    Parameters:
    -----------
    sensor_ids : list
        List of sensor device names (e.g., ['IPQ_TH14', 'IPQ_TH11'])
    start_datetime : str, optional
        Start time in ISO format (default: '2025-08-07T00:00:00Z')
    stop_datetime : str, optional
        Stop time in ISO format (default: current time)
    plot_temperature : bool, optional
        Whether to plot temperature data (default: True)
    plot_humidity : bool, optional
        Whether to plot humidity data (default: True)
    figsize : tuple, optional
        Figure size (width, height) in inches (default: (15, 10))
    save_plot : bool, optional
        Whether to save the plot to file (default: False)
    filename : str, optional
        Filename for saved plot (default: auto-generated)

    Returns:
    --------
    dict
        Dictionary containing the retrieved data for each sensor and measurement type
    """
    if not sensor_ids:
        raise ValueError("sensor_ids list cannot be empty")

    if not plot_temperature and not plot_humidity:
        raise ValueError("At least one of plot_temperature or plot_humidity must be True")

    # Color palette for different sensors
    colors = plt.cm.tab10(np.linspace(0, 1, len(sensor_ids)))

    # Store all retrieved data
    all_data = {}

    # Determine subplot configuration
    num_plots = int(plot_temperature) + int(plot_humidity)
    fig, axes = plt.subplots(num_plots, 1, figsize=figsize, sharex=True)

    if num_plots == 1:
        axes = [axes]  # Make it a list for consistent indexing

    plot_idx = 0

    # Plot temperature data
    if plot_temperature:
        ax_temp = axes[plot_idx]
        plot_idx += 1

        for i, sensor_id in enumerate(sensor_ids):
            try:
                temp_data = retrieve_sensor_data_for_measurement_type(
                    sensor_id, 'temperature', start_datetime, stop_datetime
                )

                if not temp_data.empty:
                    all_data[f"{sensor_id}_temperature"] = temp_data

                    ax_temp.plot(temp_data.index, temp_data['value'],
                               color=colors[i], linewidth=1.5, label=f'{sensor_id}', alpha=0.8)
                else:
                    print(f"No temperature data available for sensor {sensor_id}")

            except Exception as e:
                print(f"Error plotting temperature data for sensor {sensor_id}: {e}")

        ax_temp.set_ylabel('Temperature (°C)', fontsize=12)
        ax_temp.set_title('Temperature', fontsize=14, fontweight='bold')
        ax_temp.grid(True, alpha=0.3)
        ax_temp.legend(loc='upper right')

    # Plot humidity data
    if plot_humidity:
        ax_hum = axes[plot_idx]

        for i, sensor_id in enumerate(sensor_ids):
            try:
                hum_data = retrieve_sensor_data_for_measurement_type(
                    sensor_id, 'humidity', start_datetime, stop_datetime
                )

                if not hum_data.empty:
                    all_data[f"{sensor_id}_humidity"] = hum_data

                    ax_hum.plot(hum_data.index, hum_data['value'],
                              color=colors[i], linewidth=1.5, label=f'{sensor_id}', alpha=0.8)
                else:
                    print(f"No humidity data available for sensor {sensor_id}")

            except Exception as e:
                print(f"Error plotting humidity data for sensor {sensor_id}: {e}")

        ax_hum.set_ylabel('Relative Humidity (%)', fontsize=12)
        ax_hum.set_title('Humidity', fontsize=14, fontweight='bold')
        ax_hum.grid(True, alpha=0.3)
        ax_hum.legend(loc='upper right')

    # Format x-axis for the bottom plot
    bottom_ax = axes[-1]

    # Add day overlays to the bottom plot
    if all_data:
        # Get the overall time range from all data
        all_times = []
        for data in all_data.values():
            if not data.empty:
                all_times.extend(data.index.tolist())

        if all_times:
            start_date = min(all_times).normalize()
            end_date = max(all_times).normalize() + pd.Timedelta(days=1)

            # Add semi-transparent day overlays
            alphas = [0.1, 0.3]

            current_date = start_date
            day_index = 0

            while current_date < end_date:
                next_date = current_date + pd.Timedelta(days=1)
                alpha = alphas[day_index % 2]

                # Check if current day is weekend (Saturday=5, Sunday=6)
                weekday = current_date.weekday()
                if weekday in [5, 6]:  # Saturday or Sunday
                    color = 'lightblue'
                    alpha = 0.2
                else:
                    color = 'lightgray'

                # Apply overlay to all subplots
                for ax in axes:
                    ax.axvspan(current_date, next_date, color=color, alpha=alpha, zorder=0)

                current_date = next_date
                day_index += 1

            # Calculate appropriate x-axis tick interval based on data range
            def calculate_optimal_tick_locator_and_formatter(start_time, end_time, figure_width_inches=15):
                """
                Calculate optimal tick locator and formatter based on data time range.

                Parameters:
                -----------
                start_time : pandas.Timestamp
                    Start time of the data
                end_time : pandas.Timestamp
                    End time of the data
                figure_width_inches : float
                    Width of the figure in inches (used to estimate available space)

                Returns:
                --------
                tuple
                    (locator, formatter) - matplotlib locator and formatter objects
                """
                time_span = end_time - start_time
                total_hours = time_span.total_seconds() / 3600

                # Estimate available space for ticks (assuming ~1 inch per tick minimum for readability)
                # Account for margins and legend space
                available_width = figure_width_inches * 0.8  # 80% of figure width for plot area
                max_ticks = max(4, int(available_width))  # Minimum 4 ticks, maximum based on width

                # Calculate ideal interval in hours
                ideal_interval_hours = total_hours / max_ticks

                if total_hours <= 24:  # Less than 1 day - use hour intervals
                    # Round to reasonable hour intervals: 1, 2, 3, 4, 6, 8, 12
                    hour_intervals = [1, 2, 3, 4, 6, 8, 12]
                    interval = min(hour_intervals, key=lambda x: abs(x - ideal_interval_hours))
                    return mdates.HourLocator(interval=interval), mdates.DateFormatter('%H:%M')

                elif total_hours <= 168:  # Less than 1 week - use hour intervals with larger steps
                    # For 1-7 days, use 6, 12, 24 hour intervals
                    hour_intervals = [6, 12, 24]
                    interval = min(hour_intervals, key=lambda x: abs(x - ideal_interval_hours))
                    if interval >= 24:
                        return mdates.DayLocator(interval=1), mdates.DateFormatter('%m-%d')
                    else:
                        return mdates.HourLocator(interval=interval), mdates.DateFormatter('%H:%M')

                elif total_hours <= 720:  # Less than 1 month - use day intervals
                    ideal_interval_days = total_hours / 24 / max_ticks
                    day_intervals = [1, 2, 3, 7]  # 1, 2, 3 days or 1 week
                    interval = min(day_intervals, key=lambda x: abs(x - ideal_interval_days))
                    return mdates.DayLocator(interval=interval), mdates.DateFormatter('%m-%d')

                elif total_hours <= 2160:  # Less than 3 months - use week intervals
                    ideal_interval_weeks = total_hours / 24 / 7 / max_ticks
                    week_intervals = [1, 2, 4]  # 1, 2, or 4 weeks
                    interval = min(week_intervals, key=lambda x: abs(x - ideal_interval_weeks))
                    return mdates.WeekdayLocator(interval=interval), mdates.DateFormatter('%m-%d')

                else:  # More than 3 months - use month intervals
                    ideal_interval_months = total_hours / 24 / 30 / max_ticks
                    month_intervals = [1, 2, 3, 6, 12]  # 1, 2, 3, 6 months or 1 year
                    interval = min(month_intervals, key=lambda x: abs(x - ideal_interval_months))
                    return mdates.MonthLocator(interval=interval), mdates.DateFormatter('%Y-%m')

            # Get the actual data time range (not the normalized dates used for overlays)
            actual_start_time = min(all_times)
            actual_end_time = max(all_times)

            # Calculate optimal locator and formatter
            locator, formatter = calculate_optimal_tick_locator_and_formatter(
                actual_start_time, actual_end_time, figsize[0]
            )

            # Format x-axis
            bottom_ax.xaxis.set_major_formatter(formatter)
            bottom_ax.xaxis.set_major_locator(locator)
            bottom_ax.set_xlim([start_date, end_date])

            # Create secondary x-axis for date labels
            ax2 = bottom_ax.twiny()
            ax2.set_xlim(bottom_ax.get_xlim())
            ax2.xaxis.set_major_locator(mdates.DayLocator())

            # Custom formatter to include weekday abbreviation
            def format_date_with_weekday(x, pos):
                """Custom formatter to add weekday abbreviation to date labels"""
                # pos parameter required by matplotlib formatter interface but not used
                date = mdates.num2date(x)
                weekday_abbr = date.strftime('%a.')  # Get abbreviated weekday with period
                date_str = date.strftime('%m-%d')    # Get month-day format
                return f"{date_str} ({weekday_abbr})"

            ax2.xaxis.set_major_formatter(plt.FuncFormatter(format_date_with_weekday))
            ax2.xaxis.set_ticks_position('bottom')
            ax2.xaxis.set_label_position('bottom')
            ax2.tick_params(axis='x', which='major', pad=40, length=0)

            # Synchronize secondary axis with primary axis during zoom/pan operations
            def sync_secondary_axis(ax):
                """Callback function to keep secondary axis synchronized with primary axis"""
                ax2.set_xlim(ax.get_xlim())
                ax2.figure.canvas.draw_idle()

            # Connect the callback to the primary axis xlim_changed event
            bottom_ax.callbacks.connect('xlim_changed', sync_secondary_axis)

            # Rotate time labels
            plt.setp(bottom_ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            plt.setp(ax2.xaxis.get_majorticklabels(), rotation=0, ha='center')

            # Adjust font sizes
            bottom_ax.tick_params(axis='x', which='major', labelsize=9)
            ax2.tick_params(axis='x', which='major', labelsize=10)

            # Add x-axis label
            bottom_ax.set_xlabel('Date Time', labelpad=20, fontsize=12)

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)

    # Save plot if requested
    if save_plot:
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            sensor_str = '_'.join(sensor_ids)
            filename = f"multi_sensor_plot_{sensor_str}_{timestamp}.png"

        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"Plot saved as: {filename}")

    plt.show()

    return all_data


if __name__ == "__main__":
    # sensor_ids = ['IPQ_TH14', 'IPQ_TH11']   # TH14 is the one inside the Sonata
    sensor_ids = ['IPQ_TH11']

    print(f"Sensors: {sensor_ids}")

    # start_datetime = '2025-08-07T17:20:00Z'
    start_datetime = '2025-07-28T00:00:00Z'
    stop_datetime = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')

    data = plot_multiple_sensors_temperature_and_humidity(
        sensor_ids=sensor_ids,
        start_datetime=start_datetime,
        stop_datetime=stop_datetime,
        plot_temperature=True,
        plot_humidity=True,
        figsize=(15, 10)
    )