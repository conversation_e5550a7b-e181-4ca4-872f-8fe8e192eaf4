#!/usr/bin/env python3
"""
Test script to verify the dynamic tick calculation functionality in Th14.py
"""

import pandas as pd
import matplotlib.dates as mdates
from datetime import datetime, timed<PERSON><PERSON>

def calculate_optimal_tick_locator_and_formatter(start_time, end_time, figure_width_inches=15):
    """
    Calculate optimal tick locator and formatter based on data time range.
    
    Parameters:
    -----------
    start_time : pandas.Timestamp
        Start time of the data
    end_time : pandas.Timestamp  
        End time of the data
    figure_width_inches : float
        Width of the figure in inches (used to estimate available space)
        
    Returns:
    --------
    tuple
        (locator, formatter) - matplotlib locator and formatter objects
    """
    time_span = end_time - start_time
    total_hours = time_span.total_seconds() / 3600
    
    # Estimate available space for ticks (assuming ~1 inch per tick minimum for readability)
    # Account for margins and legend space
    available_width = figure_width_inches * 0.8  # 80% of figure width for plot area
    max_ticks = max(4, int(available_width))  # Minimum 4 ticks, maximum based on width
    
    # Calculate ideal interval in hours
    ideal_interval_hours = total_hours / max_ticks
    
    if total_hours <= 24:  # Less than 1 day - use hour intervals
        # Round to reasonable hour intervals: 1, 2, 3, 4, 6, 8, 12
        hour_intervals = [1, 2, 3, 4, 6, 8, 12]
        interval = min(hour_intervals, key=lambda x: abs(x - ideal_interval_hours))
        return mdates.HourLocator(interval=interval), mdates.DateFormatter('%H:%M')
        
    elif total_hours <= 168:  # Less than 1 week - use hour intervals with larger steps
        # For 1-7 days, use 6, 12, 24 hour intervals
        hour_intervals = [6, 12, 24]
        interval = min(hour_intervals, key=lambda x: abs(x - ideal_interval_hours))
        if interval >= 24:
            return mdates.DayLocator(interval=1), mdates.DateFormatter('%m-%d')
        else:
            return mdates.HourLocator(interval=interval), mdates.DateFormatter('%H:%M')
            
    elif total_hours <= 720:  # Less than 1 month - use day intervals
        ideal_interval_days = total_hours / 24 / max_ticks
        day_intervals = [1, 2, 3, 7]  # 1, 2, 3 days or 1 week
        interval = min(day_intervals, key=lambda x: abs(x - ideal_interval_days))
        return mdates.DayLocator(interval=interval), mdates.DateFormatter('%m-%d')
        
    elif total_hours <= 2160:  # Less than 3 months - use week intervals
        ideal_interval_weeks = total_hours / 24 / 7 / max_ticks
        week_intervals = [1, 2, 4]  # 1, 2, or 4 weeks
        interval = min(week_intervals, key=lambda x: abs(x - ideal_interval_weeks))
        return mdates.WeekdayLocator(interval=interval), mdates.DateFormatter('%m-%d')
        
    else:  # More than 3 months - use month intervals
        ideal_interval_months = total_hours / 24 / 30 / max_ticks
        month_intervals = [1, 2, 3, 6, 12]  # 1, 2, 3, 6 months or 1 year
        interval = min(month_intervals, key=lambda x: abs(x - ideal_interval_months))
        return mdates.MonthLocator(interval=interval), mdates.DateFormatter('%Y-%m')

def test_dynamic_tick_calculation():
    """Test the dynamic tick calculation with various time ranges"""
    
    base_time = pd.Timestamp('2025-08-01 00:00:00')
    
    test_cases = [
        # (duration_description, end_time, expected_locator_type)
        ("6 hours", base_time + timedelta(hours=6), "HourLocator"),
        ("1 day", base_time + timedelta(days=1), "HourLocator"),
        ("3 days", base_time + timedelta(days=3), "HourLocator or DayLocator"),
        ("1 week", base_time + timedelta(weeks=1), "HourLocator or DayLocator"),
        ("1 month", base_time + timedelta(days=30), "DayLocator"),
        ("3 months", base_time + timedelta(days=90), "WeekdayLocator"),
        ("1 year", base_time + timedelta(days=365), "MonthLocator"),
    ]
    
    print("Testing dynamic tick calculation:")
    print("=" * 60)
    
    for description, end_time, expected in test_cases:
        locator, formatter = calculate_optimal_tick_locator_and_formatter(base_time, end_time)
        
        locator_type = type(locator).__name__
        formatter_pattern = formatter.fmt if hasattr(formatter, 'fmt') else 'Custom'
        
        time_span = end_time - base_time
        total_hours = time_span.total_seconds() / 3600
        
        print(f"Duration: {description:12} | Hours: {total_hours:8.1f} | "
              f"Locator: {locator_type:15} | Format: {formatter_pattern}")
        
        # Get interval if available
        if hasattr(locator, 'interval'):
            print(f"                     | Interval: {locator.interval}")
        print()

if __name__ == "__main__":
    test_dynamic_tick_calculation()
